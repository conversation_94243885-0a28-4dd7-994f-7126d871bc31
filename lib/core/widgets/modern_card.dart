import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

enum ModernCardVariant {
  elevated,
  filled,
  outlined,
}

class ModernCard extends StatelessWidget {
  final Widget child;
  final ModernCardVariant variant;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final bool isSelected;
  final Widget? leading;
  final Widget? trailing;

  const ModernCard({
    super.key,
    required this.child,
    this.variant = ModernCardVariant.elevated,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.isSelected = false,
    this.leading,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Default styling based on variant
    Color? cardColor;
    double cardElevation;
    Border? border;
    
    switch (variant) {
      case ModernCardVariant.elevated:
        cardColor = backgroundColor ?? colorScheme.surface;
        cardElevation = elevation ?? 1;
        border = null;
        break;
      case ModernCardVariant.filled:
        cardColor = backgroundColor ?? colorScheme.surfaceContainer;
        cardElevation = elevation ?? 0;
        border = null;
        break;
      case ModernCardVariant.outlined:
        cardColor = backgroundColor ?? colorScheme.surface;
        cardElevation = elevation ?? 0;
        border = Border.all(
          color: colorScheme.outline,
          width: 1,
        );
        break;
    }

    // Selection styling
    if (isSelected) {
      cardColor = colorScheme.primaryContainer;
      border = Border.all(
        color: colorScheme.primary,
        width: 2,
      );
    }

    final defaultBorderRadius = borderRadius ?? 
        BorderRadius.circular(AppConstants.cardBorderRadius);

    Widget cardContent = child;
    
    // Add leading and trailing if provided
    if (leading != null || trailing != null) {
      cardContent = Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: AppConstants.defaultPadding),
          ],
          Expanded(child: child),
          if (trailing != null) ...[
            const SizedBox(width: AppConstants.defaultPadding),
            trailing!,
          ],
        ],
      );
    }

    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: AppConstants.cardMarginBottom),
      child: Material(
        color: cardColor,
        elevation: cardElevation,
        surfaceTintColor: variant == ModernCardVariant.elevated 
            ? colorScheme.primary 
            : null,
        borderRadius: defaultBorderRadius,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: defaultBorderRadius,
          child: Container(
            decoration: BoxDecoration(
              border: border,
              borderRadius: defaultBorderRadius,
            ),
            padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
            child: cardContent,
          ),
        ),
      ),
    );
  }
}

// Specialized card for list items
class ModernListCard extends StatelessWidget {
  final Widget title;
  final Widget? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ModernCardVariant variant;
  final bool isSelected;
  final EdgeInsetsGeometry? margin;

  const ModernListCard({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.variant = ModernCardVariant.elevated,
    this.isSelected = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      variant: variant,
      onTap: onTap,
      onLongPress: onLongPress,
      isSelected: isSelected,
      margin: margin,
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: AppConstants.defaultPadding),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                title,
                if (subtitle != null) ...[
                  const SizedBox(height: AppConstants.smallPadding),
                  subtitle!,
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: AppConstants.defaultPadding),
            trailing!,
          ],
        ],
      ),
    );
  }
}

// Expandable card with animation
class ModernExpandableCard extends StatefulWidget {
  final Widget header;
  final Widget expandedContent;
  final bool initiallyExpanded;
  final ModernCardVariant variant;
  final VoidCallback? onExpansionChanged;
  final EdgeInsetsGeometry? margin;

  const ModernExpandableCard({
    super.key,
    required this.header,
    required this.expandedContent,
    this.initiallyExpanded = false,
    this.variant = ModernCardVariant.elevated,
    this.onExpansionChanged,
    this.margin,
  });

  @override
  State<ModernExpandableCard> createState() => _ModernExpandableCardState();
}

class _ModernExpandableCardState extends State<ModernExpandableCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
    widget.onExpansionChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      variant: widget.variant,
      margin: widget.margin,
      padding: EdgeInsets.zero,
      child: Column(
        children: [
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppConstants.cardBorderRadius),
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  Expanded(child: widget.header),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Column(
              children: [
                Divider(
                  height: 1,
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
                Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: widget.expandedContent,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
