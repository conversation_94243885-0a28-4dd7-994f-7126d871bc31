import 'package:flutter/material.dart';

class AppTypography {
  // Private constructor to prevent instantiation
  AppTypography._();

  // Base font family - using system fonts for better performance
  static const String _fontFamily = 'Roboto';

  // Enhanced line height and letter spacing for Material 3
  static const double _defaultLineHeight = 1.4;
  static const double _tightLineHeight = 1.2;
  static const double _relaxedLineHeight = 1.6;
  static const double _defaultLetterSpacing = 0.0;
  static const double _wideLetterSpacing = 0.5;

  // Text styles for light theme - Material 3 Typography Scale
  static TextTheme get textTheme {
    return const TextTheme(
      // Display styles - largest text
      displayLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        height: _tightLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      displayMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: _defaultLetterSpacing,
        height: _tightLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      displaySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: _defaultLetterSpacing,
        height: _tightLineHeight,
        color: Color(0xFF1A1C1E),
      ),

      // Headline styles - high-emphasis text
      headlineLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 32,
        fontWeight: FontWeight.w400,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      headlineMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 28,
        fontWeight: FontWeight.w400,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      headlineSmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w400,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),

      // Title styles - medium-emphasis text
      titleLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 22,
        fontWeight: FontWeight.w400,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      titleMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w500,
        letterSpacing: _wideLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      titleSmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: _wideLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),

      // Body text - main content
      bodyLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: _wideLetterSpacing,
        height: _relaxedLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      bodyMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      bodySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        height: _defaultLineHeight,
        color: Color(0xFF42474E),
      ),

      // Labels - UI elements like buttons
      labelLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      labelMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: _wideLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF1A1C1E),
      ),
      labelSmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 10,
        fontWeight: FontWeight.w500,
        letterSpacing: _wideLetterSpacing,
        height: _defaultLineHeight,
        color: Color(0xFF42474E),
      ),
    );
  }

  // Text styles for dark theme
  static TextTheme get darkTextTheme {
    return const TextTheme(
      // Headings
      displayLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      displayMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      displaySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),

      // Body text
      bodyLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      bodyMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      bodySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white70,
      ),

      // Labels
      labelLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      labelMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      labelSmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 10,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white70,
      ),
    );
  }
}
