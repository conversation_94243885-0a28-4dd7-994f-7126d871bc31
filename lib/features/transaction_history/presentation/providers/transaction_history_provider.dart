import 'dart:developer' as developer;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../domain/entities/transaction_with_payments.dart';
import '../../../payments/domain/entities/payment.dart';
import '../../../transactions/domain/entities/transaction_item_with_details.dart';
import '../../domain/usecases/transaction_history_usecases.dart';
import 'providers.dart';

// Transaction history notifier
class TransactionHistoryNotifier extends StateNotifier<AsyncValue<List<TransactionWithPaymentsEntity>>> {
  final GetPaidTransactionsUseCase _getPaidTransactionsUseCase;
  final GetTransactionWithPaymentsUseCase _getTransactionWithPaymentsUseCase;
  final GetCompleteTransactionDetailsUseCase _getCompleteTransactionDetailsUseCase;

  TransactionHistoryNotifier({
    required GetPaidTransactionsUseCase getPaidTransactionsUseCase,
    required GetTransactionWithPaymentsUseCase getTransactionWithPaymentsUseCase,
    required GetCompleteTransactionDetailsUseCase getCompleteTransactionDetailsUseCase,
    required GetPaymentPaidItemsUseCase getPaymentPaidItemsUseCase,
  })  : _getPaidTransactionsUseCase = getPaidTransactionsUseCase,
        _getTransactionWithPaymentsUseCase = getTransactionWithPaymentsUseCase,
        _getCompleteTransactionDetailsUseCase = getCompleteTransactionDetailsUseCase,
        super(const AsyncValue.loading()) {
    refreshTransactions();
  }

  Future<void> refreshTransactions() async {
    state = const AsyncValue.loading();
    try {
      final transactions = await _getPaidTransactionsUseCase();

      // Get transaction with payments for each transaction
      final transactionsWithPayments = <TransactionWithPaymentsEntity>[];
      for (final transaction in transactions) {
        final transactionWithPayments = await _getTransactionWithPaymentsUseCase(transaction.id);
        transactionsWithPayments.add(transactionWithPayments);
      }

      state = AsyncValue.data(transactionsWithPayments);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }



  Future<List<TransactionWithPaymentsEntity>> searchTransactions(String query) async {
    if (state is! AsyncData) {
      return [];
    }

    final transactions = (state as AsyncData<List<TransactionWithPaymentsEntity>>).value;
    final results = <TransactionWithPaymentsEntity>[];

    for (final transaction in transactions) {
      // Get complete transaction details to search by item name
      final completeDetails = await _getCompleteTransactionDetailsUseCase(transaction.transaction.id);

      // Check if any item name contains the query
      final containsItem = completeDetails.items.any(
        (item) => item.item.name.toLowerCase().contains(query.toLowerCase()),
      );

      if (containsItem) {
        results.add(transaction);
      }
    }

    return results;
  }
}

// Base transaction history provider (without filtering)
final baseTransactionHistoryProvider = StateNotifierProvider<TransactionHistoryNotifier, AsyncValue<List<TransactionWithPaymentsEntity>>>((ref) {
  return TransactionHistoryNotifier(
    getPaidTransactionsUseCase: ref.watch(getPaidTransactionsUseCaseProvider),
    getTransactionWithPaymentsUseCase: ref.watch(getTransactionWithPaymentsUseCaseProvider),
    getCompleteTransactionDetailsUseCase: ref.watch(getCompleteTransactionDetailsUseCaseProvider),
    getPaymentPaidItemsUseCase: ref.watch(getPaymentPaidItemsUseCaseProvider),
  );
});

// Transaction history provider (no filtering)
final transactionHistoryProvider = Provider<AsyncValue<List<TransactionWithPaymentsEntity>>>((ref) {
  return ref.watch(baseTransactionHistoryProvider);
});

// Search transaction history provider
final searchTransactionHistoryProvider = FutureProvider.family<List<TransactionWithPaymentsEntity>, String>((ref, query) async {
  final notifier = ref.watch(baseTransactionHistoryProvider.notifier);
  return notifier.searchTransactions(query);
});

// Provider for transactions grouped by payment date with correct payment amounts
final paymentDateGroupedTransactionsProvider = Provider<AsyncValue<Map<DateTime, PaymentDateGroup>>>((ref) {
  final transactionsState = ref.watch(transactionHistoryProvider);

  return transactionsState.when(
    data: (transactions) {
      // Group transactions by payment date with actual payment amounts
      final groupedByPaymentDate = <DateTime, PaymentDateGroup>{};

      // First pass: collect all payments by date
      final paymentsByDate = <DateTime, List<PaymentEntity>>{};
      final transactionsByDate = <DateTime, Set<TransactionWithPaymentsEntity>>{};

      for (final transaction in transactions) {
        for (final payment in transaction.payments) {
          // Use payment date as the key (normalized to day)
          final paymentDateKey = DateTime(payment.date.year, payment.date.month, payment.date.day);

          // Add payment to the date
          paymentsByDate.putIfAbsent(paymentDateKey, () => []).add(payment);

          // Add transaction to the date (using Set to avoid duplicates)
          transactionsByDate.putIfAbsent(paymentDateKey, () => {}).add(transaction);
        }
      }

      // Second pass: create PaymentDateGroup for each date
      for (final entry in paymentsByDate.entries) {
        final date = entry.key;
        final paymentsForDate = entry.value;
        final transactionsForDate = transactionsByDate[date]?.toList() ?? [];

        // Calculate total payment amount for this date
        final totalPaymentAmount = paymentsForDate.fold(0.0, (sum, payment) => sum + payment.amount);

        groupedByPaymentDate[date] = PaymentDateGroup(
          date: date,
          transactions: transactionsForDate,
          totalPaymentAmount: totalPaymentAmount,
        );
      }

      return AsyncValue.data(groupedByPaymentDate);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Class to hold payment date group data
class PaymentDateGroup {
  final DateTime date;
  final List<TransactionWithPaymentsEntity> transactions;
  final double totalPaymentAmount;

  PaymentDateGroup({
    required this.date,
    required this.transactions,
    required this.totalPaymentAmount,
  });
}

// Class to hold grouped item data for display
class GroupedItemData {
  final String itemName;
  final int totalQuantity;
  final double totalAmount;

  GroupedItemData({
    required this.itemName,
    required this.totalQuantity,
    required this.totalAmount,
  });
}

// Provider for getting paid items grouped by item name for a specific payment date
final paidItemsByDateProvider = FutureProvider.family<List<GroupedItemData>, DateTime>((ref, date) async {
  final transactionsState = ref.watch(transactionHistoryProvider);

  if (transactionsState is! AsyncData) {
    return [];
  }

  final transactions = transactionsState.value ?? [];
  final groupedItems = <String, GroupedItemData>{};

  for (final transaction in transactions) {
    for (final payment in transaction.payments) {
      try {
        // Check if this payment was made on the specified date
        final paymentDate = DateTime(payment.date.year, payment.date.month, payment.date.day);
        final targetDate = DateTime(date.year, date.month, date.day);

        if (paymentDate.isAtSameMomentAs(targetDate)) {
          try {
            // Get the paid items for this payment
            final paidItems = await ref.read(getPaymentPaidItemsUseCaseProvider).call(payment.id);

            // If we have paid items records, use them
            if (paidItems.isNotEmpty) {
              // Create a map of all transaction item details for efficient lookup
              final Map<int, TransactionItemWithDetailsEntity> transactionItemsMap = {};

              // Collect all transaction item details from all transactions
              for (final searchTransaction in transactions) {
                try {
                  final searchTransactionDetails = await ref.read(getCompleteTransactionDetailsUseCaseProvider).call(searchTransaction.transaction.id);

                  for (final item in searchTransactionDetails.items) {
                    transactionItemsMap[item.transactionItem.id] = item;
                  }
                } catch (e) {
                  // Continue with other transactions if one fails
                  continue;
                }
              }

              // Process each paid item using the pre-built map
              for (final paidItem in paidItems) {
                try {
                  final transactionItemDetails = transactionItemsMap[paidItem.transactionItemId];

                  if (transactionItemDetails != null) {
                    final itemName = transactionItemDetails.item.name;

                    if (!groupedItems.containsKey(itemName)) {
                      groupedItems[itemName] = GroupedItemData(
                        itemName: itemName,
                        totalQuantity: 0,
                        totalAmount: 0,
                      );
                    }

                    // Update the grouped item data
                    final currentData = groupedItems[itemName]!;
                    groupedItems[itemName] = GroupedItemData(
                      itemName: itemName,
                      totalQuantity: currentData.totalQuantity + paidItem.quantity,
                      totalAmount: currentData.totalAmount + paidItem.amount,
                    );
                  }
                } catch (e) {
                  // Skip this paid item if there's an error
                  developer.log('Error processing paid item: $e');
                  continue;
                }
              }
            } else {
              // Fallback: If no paid items records, use transaction items as fallback
              // This handles payments made before the paid items system was implemented
              if (transaction.transaction.status == AppConstants.statusPaid) {
                try {
                  final transactionDetails = await ref.read(getCompleteTransactionDetailsUseCaseProvider).call(transaction.transaction.id);

                  // Only use fully paid transactions for fallback
                  for (final item in transactionDetails.items) {
                    final itemName = item.item.name;
                    final itemQuantity = item.transactionItem.quantity;
                    final itemAmount = (item.transactionItem.quantity * item.transactionItem.priceAtPurchase);

                    if (!groupedItems.containsKey(itemName)) {
                      groupedItems[itemName] = GroupedItemData(
                        itemName: itemName,
                        totalQuantity: 0,
                        totalAmount: 0,
                      );
                    }

                    // Update the grouped item data
                    final currentData = groupedItems[itemName]!;
                    groupedItems[itemName] = GroupedItemData(
                      itemName: itemName,
                      totalQuantity: currentData.totalQuantity + itemQuantity,
                      totalAmount: currentData.totalAmount + itemAmount,
                    );
                  }
                } catch (e) {
                  developer.log('Error getting transaction details for fallback: $e');
                }
              }
            }
          } catch (e) {
            // Fallback: If error getting paid items, use transaction items as fallback
            developer.log('Error getting paid items for payment ${payment.id}: $e');
            if (transaction.transaction.status == AppConstants.statusPaid) {
              try {
                final transactionDetails = await ref.read(getCompleteTransactionDetailsUseCaseProvider).call(transaction.transaction.id);

                // Only use fully paid transactions for fallback
                for (final item in transactionDetails.items) {
                  final itemName = item.item.name;
                  final itemQuantity = item.transactionItem.quantity;
                  final itemAmount = (item.transactionItem.quantity * item.transactionItem.priceAtPurchase);

                  if (!groupedItems.containsKey(itemName)) {
                    groupedItems[itemName] = GroupedItemData(
                      itemName: itemName,
                      totalQuantity: 0,
                      totalAmount: 0,
                    );
                  }

                  // Update the grouped item data
                  final currentData = groupedItems[itemName]!;
                  groupedItems[itemName] = GroupedItemData(
                    itemName: itemName,
                    totalQuantity: currentData.totalQuantity + itemQuantity,
                    totalAmount: currentData.totalAmount + itemAmount,
                  );
                }
              } catch (e) {
                developer.log('Error getting transaction details for fallback: $e');
              }
            }
          }
        }
      } catch (e) {
        // Skip this payment if there's an error
        developer.log('Error processing payment: $e');
        continue;
      }
    }
  }

  // Convert the map to a list and sort by item name
  final result = groupedItems.values.toList()
    ..sort((a, b) => a.itemName.compareTo(b.itemName));

  return result;
});


