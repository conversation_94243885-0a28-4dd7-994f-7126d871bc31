import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../core/widgets/modern_card.dart';
import '../../domain/entities/transaction_with_payments.dart';
import '../providers/transaction_history_provider.dart' as provider;

class PaymentHistoryExpandableCard extends ConsumerStatefulWidget {
  final DateTime date;
  final double totalPaymentAmount;
  final List<TransactionWithPaymentsEntity> transactions;

  const PaymentHistoryExpandableCard({
    super.key,
    required this.date,
    required this.totalPaymentAmount,
    required this.transactions,
  });

  @override
  ConsumerState<PaymentHistoryExpandableCard> createState() => _PaymentHistoryExpandableCardState();
}

class _PaymentHistoryExpandableCardState extends ConsumerState<PaymentHistoryExpandableCard> {
  bool _isExpanded = false;

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ModernExpandableCard(
      initiallyExpanded: _isExpanded,
      onExpansionChanged: _toggleExpanded,
      header: Row(
        children: [
          // Date icon and info
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  color: theme.colorScheme.onPrimaryContainer,
                  size: 20,
                ),
                Text(
                  widget.date.day.toString(),
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          // Date and transaction info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Formatters.formatDate(widget.date),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.transactions.length} transaction${widget.transactions.length != 1 ? 's' : ''}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          // Total payment amount
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                Formatters.formatCurrency(widget.totalPaymentAmount),
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Total Payment',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
      expandedContent: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Items paid on this date:',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.mediumPadding),
          _buildCombinedItemsList(),
        ],
      ),
    );
  }

  Widget _buildCombinedItemsList() {
    return Consumer(
      builder: (context, ref, child) {
        final paidItemsAsyncValue = ref.watch(provider.paidItemsByDateProvider(widget.date));

        return paidItemsAsyncValue.when(
          data: (groupedItems) {
            if (groupedItems.isEmpty) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
                child: Text(
                  'No items found for this payment date',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                ),
              );
            }

            return Column(
              children: groupedItems.map((groupedItem) {
                return Container(
                  margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.mediumPadding,
                    vertical: AppConstants.smallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerLow.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              groupedItem.itemName,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '${groupedItem.totalQuantity} × ${Formatters.formatCurrency(groupedItem.totalAmount / groupedItem.totalQuantity)}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        Formatters.formatCurrency(groupedItem.totalAmount),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            );
          },
          loading: () => const Padding(
            padding: EdgeInsets.symmetric(vertical: AppConstants.defaultPadding),
            child: Center(
              child: SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          ),
          error: (error, stackTrace) => Padding(
            padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
            child: Text(
              'Error loading items: $error',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
            ),
          ),
        );
      },
    );
  }
}
